Scope: all 20 workspace projects
Progress: resolved 1, reused 0, downloaded 0, added 0
Progress: resolved 7, reused 0, downloaded 0, added 0
Progress: resolved 8, reused 0, downloaded 0, added 0
Progress: resolved 11, reused 0, downloaded 0, added 0
Progress: resolved 14, reused 0, downloaded 0, added 0
Progress: resolved 15, reused 0, downloaded 0, added 0
Progress: resolved 18, reused 0, downloaded 0, added 0
Progress: resolved 19, reused 0, downloaded 0, added 0
Progress: resolved 21, reused 0, downloaded 0, added 0
 WARN  deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
Progress: resolved 35, reused 0, downloaded 0, added 0
workspace/resources/server-globals       |  WARN  deprecated @types/redis@4.0.11
Progress: resolved 45, reused 0, downloaded 0, added 0
workspace/resources/server-globals       |  WARN  deprecated multer@1.4.5-lts.2
Progress: resolved 63, reused 0, downloaded 0, added 0
Progress: resolved 87, reused 0, downloaded 0, added 0
Progress: resolved 120, reused 0, downloaded 0, added 0
workspace/clients/web                    |  WARN  deprecated svg2png-wasm@1.4.1
Progress: resolved 144, reused 0, downloaded 0, added 0
workspace/clients/embed                  |  WARN  deprecated @types/dompurify@3.2.0
Progress: resolved 178, reused 1, downloaded 0, added 0
workspace/servers/public-api             |  WARN  deprecated supertest@6.3.4
workspace/servers/public-api-live        |  WARN  deprecated @types/cookie@1.0.0
workspace/servers/public-api             |  WARN  deprecated @distube/ytdl-core@4.16.8
Progress: resolved 193, reused 1, downloaded 0, added 0
Progress: resolved 195, reused 1, downloaded 0, added 0
Progress: resolved 197, reused 1, downloaded 0, added 0
Progress: resolved 198, reused 1, downloaded 0, added 0
Progress: resolved 1421, reused 1, downloaded 0, added 0
Progress: resolved 2370, reused 1, downloaded 0, added 0
 WARN  22 deprecated subdependencies found: @humanwhocodes/config-array@0.13.0, @humanwhocodes/object-schema@2.0.3, @npmcli/move-file@2.0.1, @types/long@5.0.0, abab@2.0.6, are-we-there-yet@3.0.1, boolean@3.2.0, domexception@4.0.0, formidable@2.1.2, gauge@4.0.4, glob@7.2.3, glob@8.1.0, google-p12-pem@4.0.1, inflight@1.0.6, lodash.get@4.4.2, node-domexception@1.0.0, npmlog@6.0.2, read-package-json@7.0.1, rimraf@2.7.1, rimraf@3.0.2, superagent@8.1.2, trim@0.0.1

Progress: resolved 2387, reused 77, downloaded 0, added 0
Packages: +6 -96
++++----------------------------------------------------------------------------
Progress: resolved 2387, reused 104, downloaded 0, added 6, done
workspace/resources/utils prepare$ rimraf ./dist && tsc
. prepare$ husky
. prepare: Done
workspace/resources/utils prepare: Done
workspace/resources/server-utils prepare$ rimraf ./dist && tsc
workspace/resources/models prepare$ rimraf ./dist && tsc
workspace/resources/models prepare: Done
workspace/resources/server-utils prepare: Done
workspace/resources/actions prepare$ rimraf ./dist && tsc
workspace/clients/embed prepare$ rimraf ./dist && tsc --project tsconfig.json
workspace/resources/tools prepare$ rimraf ./dist && tsc
workspace/resources/server-globals prepare$ rimraf ./dist && tsc
workspace/resources/actions prepare: Done
workspace/clients/embed prepare: Done
workspace/resources/server-globals prepare: Done
workspace/resources/tools prepare: Done
workspace/clients/web prepare$ rimraf ./dist && tsc --skipLibCheck
workspace/resources/server-tools prepare$ rimraf ./dist && tsc
workspace/resources/server-tools prepare: Done
workspace/clients/web prepare: Done
workspace/resources/server-models prepare$ rimraf ./dist && tsc
workspace/clients/tests prepare$ rimraf ./dist && tsc && node scripts/copy-html.js || echo 'TypeScript errors ignored'
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/no-release-chat-only.ts(7,10): error TS2724: '"../../../../../story-test/workbench/release/actions"' has no exported member named 'createRelease'. Did you mean 'getRelease'?
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/no-release-chat-only.ts(7,25): error TS2305: Module '"../../../../../story-test/workbench/release/actions"' has no exported member 'getReleaseThread'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/no-release-chat-only.ts(10,9): error TS2488: Type 'Promise<({ (input: URL | RequestInfo, init?: RequestInit | undefined): Promise<Response>; (input: string | URL | Request, init?: RequestInit | undefined): Promise<...>; } & { ...; } & UserConfig & GroupConfig)[]>' must have a '[Symbol.iterator]()' method that returns an iterator.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/no-release-chat-only.ts(13,22): error TS2554: Expected 2 arguments, but got 1.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/no-release-chat-only.ts(14,9): error TS2304: Cannot find name 'addReleaseToChat'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/no-release-chat-only.ts(15,9): error TS2304: Cannot find name 'addReleaseChat'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/no-release-chat-only.ts(18,15): error TS2345: Argument of type 'ChatResult' is not assignable to parameter of type '{ chatId: string; }'.
workspace/clients/tests prepare:   Property 'chatId' is missing in type 'ChatResult' but required in type '{ chatId: string; }'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/no-release-chat-only.ts(22,61): error TS7006: Parameter 'message' implicitly has an 'any' type.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/no-release-chat-only.ts(25,53): error TS2339: Property '_id' does not exist on type 'ChatResult'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/no-release-chat-only.ts(31,9): error TS2488: Type 'Promise<({ (input: URL | RequestInfo, init?: RequestInit | undefined): Promise<Response>; (input: string | URL | Request, init?: RequestInit | undefined): Promise<...>; } & { ...; } & UserConfig & GroupConfig)[]>' must have a '[Symbol.iterator]()' method that returns an iterator.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/no-release-chat-only.ts(34,22): error TS2554: Expected 2 arguments, but got 1.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/no-release-chat-only.ts(37,11): error TS2304: Cannot find name 'addReleaseThreadUserToUserMessage'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/no-release-chat-only.ts(45,53): error TS2339: Property '_id' does not exist on type 'ChatResult'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(8,35): error TS2304: Cannot find name 'getUsers'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(10,19): error TS2304: Cannot find name 'createRelease'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(11,22): error TS2304: Cannot find name 'createChat'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(12,9): error TS2304: Cannot find name 'addReleaseToChat'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(25,46): error TS2304: Cannot find name 'addReleaseThreadUserToUserMessage'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(37,42): error TS2304: Cannot find name 'AuthFetch'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(37,66): error TS2304: Cannot find name 'AuthFetch'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(38,30): error TS2304: Cannot find name 'ChatResult'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(38,51): error TS2304: Cannot find name 'Release'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(42,30): error TS2304: Cannot find name 'getChat'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(43,66): error TS7006: Parameter 'message' implicitly has an 'any' type.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(46,74): error TS7006: Parameter 'message' implicitly has an 'any' type.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(48,70): error TS7006: Parameter 'message' implicitly has an 'any' type.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(52,33): error TS2304: Cannot find name 'getReleaseThread'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(53,72): error TS7006: Parameter 'message' implicitly has an 'any' type.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(56,80): error TS7006: Parameter 'message' implicitly has an 'any' type.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/release-chat.ts(58,76): error TS7006: Parameter 'message' implicitly has an 'any' type.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/user-has-permission.ts(10,38): error TS2304: Cannot find name 'getUsers'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/user-has-permission.ts(12,22): error TS2304: Cannot find name 'createChat'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/user-has-permission.ts(24,9): error TS2554: Expected 4 arguments, but got 3.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/user-has-permission.ts(31,30): error TS2304: Cannot find name 'getChat'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/user-has-permission.ts(32,66): error TS7006: Parameter 'message' implicitly has an 'any' type.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/user-has-permission.ts(38,51): error TS2304: Cannot find name 'getUsers'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/user-has-permission.ts(40,22): error TS2304: Cannot find name 'createChat'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/user-has-permission.ts(41,9): error TS2554: Expected 4 arguments, but got 3.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/user-has-permission.ts(43,19): error TS2304: Cannot find name 'createRelease'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/user-has-permission.ts(54,28): error TS2304: Cannot find name 'getChat'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/user-has-permission.ts(55,3): error TS2304: Cannot find name 't'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/user-has-permission.ts(57,9): error TS2304: Cannot find name 'addReleaseToChat'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/user-has-permission.ts(64,24): error TS2304: Cannot find name 'getChat'.
workspace/clients/tests prepare: src/tests/ai-chat/tests/free-write/social-messages/user-has-permission.ts(65,66): error TS7006: Parameter 'message' implicitly has an 'any' type.
workspace/clients/tests prepare: TypeScript errors ignored
workspace/clients/tests prepare: Done
workspace/resources/server-models prepare: Done
workspace/resources/server-permissions prepare$ rimraf ./dist && tsc
workspace/servers/public-api-webhook prepare$ rimraf ./dist && tsc
workspace/servers/test-api prepare$ rimraf ./dist && tsc || echo 'ℹ️ TypeScript errors ignored'
workspace/resources/server-permissions prepare: Done
workspace/servers/test-api prepare: Done
workspace/servers/public-api-webhook prepare: Done
workspace/servers/public-api-live prepare$ rimraf ./dist && tsc
workspace/servers/public-api prepare$ rimraf ./dist && tsc
workspace/servers/public-api-live prepare: Done
workspace/servers/public-api prepare: Done
Done in 1m 4.6s using pnpm v10.11.0
