workspace/resources/models               |  WARN  The field "pnpm.neverBuiltDependencies" was found in /Users/<USER>/Documents/server2/server/workspace/resources/models/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/server-globals       |  WARN  The field "pnpm.neverBuiltDependencies" was found in /Users/<USER>/Documents/server2/server/workspace/resources/server-globals/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/server-permissions   |  WARN  The field "pnpm.neverBuiltDependencies" was found in /Users/<USER>/Documents/server2/server/workspace/resources/server-permissions/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/server-tools         |  WARN  The field "pnpm.neverBuiltDependencies" was found in /Users/<USER>/Documents/server2/server/workspace/resources/server-tools/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
workspace/resources/utils                |  WARN  The field "pnpm.neverBuiltDependencies" was found in /Users/<USER>/Documents/server2/server/workspace/resources/utils/package.json. This will not take effect. You should configure "pnpm.neverBuiltDependencies" at the root of the workspace instead.
Scope: all 19 workspace projects
Progress: resolved 1, reused 0, downloaded 0, added 0
 WARN  deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
workspace/clients/embed                  |  WARN  deprecated @types/dompurify@3.2.0
Progress: resolved 124, reused 0, downloaded 0, added 0
workspace/resources/server-globals       |  WARN  deprecated @types/redis@4.0.11
workspace/resources/server-globals       |  WARN  deprecated multer@1.4.5-lts.2
workspace/servers/public-api-live        |  WARN  deprecated @types/cookie@1.0.0
Progress: resolved 1029, reused 1, downloaded 0, added 0
 WARN  15 deprecated subdependencies found: @humanwhocodes/config-array@0.13.0, @humanwhocodes/object-schema@2.0.3, @types/long@5.0.0, abab@2.0.6, domexception@4.0.0, glob@7.2.3, google-p12-pem@4.0.1, inflight@1.0.6, lodash.get@4.4.2, node-domexception@1.0.0, read-package-json@7.0.1, rimraf@2.7.1, rimraf@3.0.2, superagent@8.1.2, trim@0.0.1
Progress: resolved 2119, reused 1, downloaded 0, added 0

Packages: +4 -76
++++----------------------------------------------------------------------------
Progress: resolved 2119, reused 82, downloaded 0, added 4, done
workspace/resources/utils prepare$ rimraf ./dist && tsc
. prepare$ husky
. prepare: Done
workspace/resources/utils prepare: Done
workspace/resources/models prepare$ rimraf ./dist && tsc
workspace/resources/server-utils prepare$ rimraf ./dist && tsc
workspace/resources/models prepare: Done
workspace/resources/server-utils prepare: Done
workspace/resources/actions prepare$ rimraf ./dist && tsc
workspace/resources/tools prepare$ rimraf ./dist && tsc
workspace/clients/embed prepare$ rimraf ./dist && tsc --project tsconfig.json
workspace/resources/server-globals prepare$ rimraf ./dist && tsc
workspace/resources/tools prepare: Done
workspace/resources/actions prepare: Done
workspace/clients/embed prepare: Done
workspace/resources/server-globals prepare: Done
workspace/clients/web prepare$ rimraf ./dist && tsc --skipLibCheck
workspace/resources/mtls prepare$ npm run build
workspace/resources/server-tools prepare$ rimraf ./dist && tsc --skipLibCheck
workspace/resources/mtls prepare: > @divinci-ai/mtls@0.1.0 build
workspace/resources/mtls prepare: > tsc
workspace/resources/mtls prepare: Done
workspace/resources/server-tools prepare: Done
workspace/clients/web prepare: src/pages/Chat/ChatIndex/ChatCreateForm/MessageAssistants.tsx(54,37): error TS18047: 'selectedAssistant' is possibly 'null'.
workspace/clients/web prepare: Failed
 ELIFECYCLE  Command failed with exit code 2.
