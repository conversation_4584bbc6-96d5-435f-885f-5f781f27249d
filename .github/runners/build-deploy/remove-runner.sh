#!/bin/bash

# 🧹 Runner Removal <PERSON>t
# =======================
#
# 🎯 Purpose:
# Remove GitHub self-hosted runners that are stuck, offline, or need cleanup.
#
# 📋 Usage:
#   ./remove-runner.sh <command>
#
# 💡 Commands:
#   list         - List all runners and their IDs
#   <runner_id>  - Remove a specific runner by ID
#
# 🔧 Configuration:
#   - Requires .env file in the same directory
#   - Uses DIVINCI_PAT_3 from .env for authentication
#
# 📝 Examples:
#   # List all runners
#   ./remove-runner.sh list
#
#   # Remove runner with ID 123456
#   ./remove-runner.sh 123456
#
# 🚨 Note:
#   This script will forcefully remove runners even if they're offline.
#   Use with caution as removed runners cannot be restored.
#
# 🔑 Required Environment Variables (in .env):
#   - DIVINCI_PAT_3: GitHub Personal Access Token
#   - GITHUB_OWNER: Repository owner (e.g., Divinci-AI)
#   - GITHUB_REPOSITORY: Repository name (e.g., server)
#
# 📄 Example .env format:
#   DIVINCI_PAT_3=ghp_your_token_here
#   GITHUB_OWNER=Divinci-AI
#   GITHUB_REPOSITORY=server
#
# 🏷️ Version: 1.0.0
# 👨‍💻 Author: Divinci-AI Team
# 📅 Last Updated: 2024

# Check if RUNNER_ID is provided
if [ -z "$1" ]; then
    echo "❌ Error: RUNNER_ID is required."
    echo "Usage: ./remove-runner.sh RUNNER_ID"
    echo ""
    echo "To list available runners, run: ./remove-runner.sh list."
    exit 1
fi

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Load environment variables from .env file
if [ -f "${SCRIPT_DIR}/.env" ]; then
    echo "📄 Loading environment variables from .env"
    export $(cat "${SCRIPT_DIR}/.env" | grep -v '^#' | xargs)
else
    echo "❌ Error: .env file not found in ${SCRIPT_DIR}"
    echo "Please copy .env.example to .env and fill in the required values."
    exit 1
fi

# Check if DIVINCI_PAT_3 is set
if [ -z "$DIVINCI_PAT_3" ]; then
    echo "❌ Error: DIVINCI_PAT_3 not found in .env file."
    exit 1
fi

# List runners if requested
if [ "$1" = "list" ]; then
    echo "🔍 Listing available runners:"
    echo "----------------------------"
    curl -s -H "Authorization: token ${DIVINCI_PAT_3}" \
         -H "Accept: application/vnd.github.v3+json" \
         "https://api.github.com/repos/${GITHUB_OWNER}/${GITHUB_REPOSITORY}/actions/runners" | \
         jq -r '.runners[] | "ID: \(.id)\tName: \(.name)\tStatus: \(.status)\tBusy: \(.busy)"' | \
         column -t -s $'\t'
    echo "----------------------------"
    echo "👉 To remove a runner, use: ./remove-runner.sh <ID>"
    exit 0
fi

# Configuration
RUNNER_ID="$1"

echo "🔍 Attempting to remove runner ID: ${RUNNER_ID}"
echo "⚠️  This action cannot be undone!"
echo -n "❓ Are you sure you want to continue? (y/N) "
read -r confirm

if [[ ! $confirm =~ ^[Yy]$ ]]; then
    echo "🛑 Operation cancelled."
    exit 1
fi

# Force remove the runner
response=$(curl -s -w "%{http_code}" -X DELETE \
    -H "Authorization: token ${DIVINCI_PAT_3}" \
    -H "Accept: application/vnd.github.v3+json" \
    "https://api.github.com/repos/${GITHUB_OWNER}/${GITHUB_REPOSITORY}/actions/runners/${RUNNER_ID}")

http_code=${response: -3}
body=${response:0:${#response}-3}

if [ "$http_code" -eq 204 ]; then
    echo "✅ Runner ${RUNNER_ID} successfully removed."
else
    echo "❌ Failed to remove runner ${RUNNER_ID}"
    echo "HTTP Status: ${http_code}"
    echo "Response: ${body}"
    exit 1
fi
