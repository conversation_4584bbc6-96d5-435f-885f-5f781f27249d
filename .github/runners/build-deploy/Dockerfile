FROM myoung34/github-runner:debian-bookworm

# Install essential packages first
RUN apt-get update && apt-get install -y \
  curl \
  coreutils \
  net-tools \
  lsof \
  && rm -rf /var/lib/apt/lists/*

# Install Node.js 20
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
  && apt-get update \
  && apt-get install -y nodejs \
  && node --version

# Update npm
RUN npm install -g npm

# Install Playwright and its dependencies
RUN npm install -g playwright@latest \
  && npx playwright install --with-deps chromium

# Ensure entrypoint script has correct permissions
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
