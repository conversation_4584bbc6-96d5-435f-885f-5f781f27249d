#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to log messages with timestamps
log() {
    echo -e "[$(date +'%Y-%m-%d %H:%M:%S')] ${1}"
}

# Function to stop and remove existing runner container
cleanup_existing() {
    log "${YELLOW}Cleaning up existing runner container...${NC}"
    docker stop github-runner 2>/dev/null || true
    docker rm github-runner 2>/dev/null || true
}

# Function to rebuild and start runner
rebuild_runner() {
    log "${YELLOW}Building new runner container...${NC}"

    # Build the runner image
    docker build -t github-runner \
        -f Dockerfile .

    log "${YELLOW}Starting new runner container...${NC}"

    # Start the runner container
    docker run -d --restart always \
        --name github-runner \
        -e REPO_URL="https://github.com/Divinci-AI/server" \
        -e RUNNER_NAME="${RUNNER_NAME}" \
        -e RUNNER_TOKEN="${ACCESS_TOKEN}" \
        -e RUNNER_WORKDIR="${RUNNER_WORKDIR}" \
        -e LABELS="${RUNNER_LABELS}" \
        -v /var/run/docker.sock:/var/run/docker.sock \
        -v ${RUNNER_WORKDIR}:${RUNNER_WORKDIR} \
        github-runner

    # Check if container started successfully
    if [ $? -eq 0 ]; then
        log "${GREEN}Runner container started successfully${NC}"
    else
        log "${RED}Failed to start runner container${NC}"
        exit 1
    fi
}

# Main execution
main() {
    log "${GREEN}Starting runner rebuild process...${NC}"

    # Load environment variables
    if [[ -f .env ]]; then
        set -a
        source .env
        set +a
    else
        echo "Error: .env file not found"
        exit 1
    fi

    # Clean up existing runner
    cleanup_existing

    # Rebuild and start runner
    rebuild_runner

    log "${GREEN}Runner rebuild process completed successfully${NC}"
}

# Execute main function
main "$@"
