# server/.github/runners/build-deploy/healthy.sh
#!/bin/bash
set -e

# Log environment for debugging
echo "Debug Info:"
echo "RUNNER_NAME: $RUNNER_NAME"
echo "REPO_URL: $REPO_URL"
echo "Hostname: $(hostname)"
echo "Architecture: $(uname -m)"

repo_path="https://github.com"
api_path="https://api.github.com/repos"
api_query=`echo $REPO_URL | sed "s|$repo_path|$api_path|g"`"/actions/runners"

echo "Checking runner status for $RUNNER_NAME... "

response=$(curl -s -H "Authorization: token ${GITHUB_PAT}" ${api_query})
if [ $? -ne 0 ]; then
    echo "Failed to query GitHub API"
    exit 1
fi

status=$(echo "$response" | jq -r '.runners[] | select(.name=="'$RUNNER_NAME'") | .status')
if [ -z "$status" ]; then
    echo "Runner $RUNNER_NAME not found"
    exit 1
fi

echo "Runner status: $status"
if [[ $status == "online" ]]; then
    exit 0
fi

# Check for required dependencies
command -v jq >/dev/null 2>&1 || { echo "jq is required but not installed"; exit 1; }
command -v curl >/dev/null 2>&1 || { echo "curl is required but not installed"; exit 1; }

# Check for required environment variables
if [ -z "$REPO_URL" ]; then
    echo "REPO_URL environment variable is not set"
    exit 1
fi

if [ -z "$GITHUB_PAT" ]; then
    echo "GITHUB_PAT environment variable is not set"
    exit 1
fi

if [ -z "$RUNNER_NAME" ]; then
    echo "RUNNER_NAME environment variable is not set"
    exit 1
fi

# Debug info
echo "Available runners:"
curl -s \
  -H "Authorization: token ${GITHUB_PAT}" \
  -H "Accept: application/vnd.github.v3+json" \
  "https://api.github.com/repos/${GITHUB_REPOSITORY}/actions/runners" | jq -r '.runners[] | {name, status}'

# Check for any runner starting with divinci-docker-runner
if curl -s \
     -H "Authorization: token ${GITHUB_PAT}" \
     -H "Accept: application/vnd.github.v3+json" \
     "https://api.github.com/repos/${GITHUB_REPOSITORY}/actions/runners" | \
     jq -r '.runners[] | select(.name | startswith("divinci-docker-runner")) | select(.status=="online")' | \
     grep -q .; then
  echo "Found available runner"
  exit 0
else
  echo "No available runners found"
  exit 1
fi

exit 0
