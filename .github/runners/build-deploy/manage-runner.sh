#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to log messages
log() {
    echo -e "[$(date +'%Y-%m-%d %H:%M:%S')] ${1}"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [command]"
    echo "Commands:"
    echo "  start   - Start the runner"
    echo "  stop    - Stop the runner"
    echo "  restart - Restart the runner"
    echo "  status  - Check runner status"
    echo "  logs    - Show runner logs"
    echo "  rebuild - Rebuild and restart runner"
}

# Function to check runner status
check_status() {
    if docker ps -q -f name=github-runner >/dev/null; then
        log "${GREEN}Runner is running${NC}"
        docker ps -f name=github-runner
    else
        log "${RED}Runner is not running${NC}"
    fi
}

# Function to start runner
start_runner() {
    if docker ps -q -f name=github-runner >/dev/null; then
        log "${YELLOW}Runner is already running${NC}"
    else
        log "${GREEN}Starting runner...${NC}"
        docker start github-runner
    fi
}

# Function to stop runner
stop_runner() {
    if docker ps -q -f name=github-runner >/dev/null; then
        log "${YELLOW}Stopping runner...${NC}"
        docker stop github-runner
    else
        log "${RED}Runner is not running${NC}"
    fi
}

# Main execution
case "$1" in
    start)
        start_runner
        ;;
    stop)
        stop_runner
        ;;
    restart)
        stop_runner
        start_runner
        ;;
    status)
        check_status
        ;;
    logs)
        docker logs -f github-runner
        ;;
    rebuild)
        ./rebuild-runner.sh
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
