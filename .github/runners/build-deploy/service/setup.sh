#!/bin/bash

# service-setup.sh
set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
RUNNER_DIR="$(dirname "${SCRIPT_DIR}")"  # Parent directory of service/
COMPOSE_FILE="${RUNNER_DIR}/build-deploy_docker-compose.yml"

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_status $RED "Please run as root (sudo)"
        exit 1
    fi
}

# Function to detect OS
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ -f /etc/os-release ]]; then
        echo "linux"
    else
        echo "unknown"
    fi
}

check_existing_service() {
    if launchctl list | grep -q "com.github.runner"; then
        print_status $YELLOW "Service already exists. Stopping and removing..."
        launchctl stop com.github.runner
        launchctl unload /Library/LaunchDaemons/com.github.runner.plist
        rm -f /Library/LaunchDaemons/com.github.runner.plist
    fi
}

# Function to setup macOS service
setup_macos() {
    print_status $GREEN "Setting up macOS LaunchDaemon..."
    check_existing_service

    # Get current user
    CURRENT_USER=$(logname || whoami)

    cat > /Library/LaunchDaemons/com.github.runner.plist << EOL
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.github.runner</string>
    <key>ProgramArguments</key>
    <array>
        <string>/bin/bash</string>
        <string>-c</string>
        <string>source ~/.zshrc && cd ${RUNNER_DIR} && DOCKER_BUILDKIT=0 docker-compose -f ${COMPOSE_FILE} up --build</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <dict>
        <key>SuccessfulExit</key>
        <false/>
        <key>Crashed</key>
        <true/>
        <key>ThrottleInterval</key>
        <integer>60</integer>
    </dict>
    <key>ThrottleInterval</key>
    <integer>60</integer>
    <key>WorkingDirectory</key>
    <string>${SCRIPT_DIR}</string>
    <key>StandardErrorPath</key>
    <string>/Users/<USER>/Library/Logs/github-runner.err</string>
    <key>StandardOutPath</key>
    <string>/Users/<USER>/Library/Logs/github-runner.log</string>
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/opt/homebrew/bin</string>
        <key>HOME</key>
        <string>/Users/<USER>/string>
    </dict>
</dict>
</plist>
EOL

    # Set permissions
    chown root:wheel /Library/LaunchDaemons/com.github.runner.plist
    chmod 644 /Library/LaunchDaemons/com.github.runner.plist

    # Create log files
    touch /Users/<USER>/Library/Logs/github-runner.log
    touch /Users/<USER>/Library/Logs/github-runner.err
    chown ${CURRENT_USER}:staff /Users/<USER>/Library/Logs/github-runner.*
    chmod 644 /Users/<USER>/Library/Logs/github-runner.*

    # Load and start service
    launchctl load /Library/LaunchDaemons/com.github.runner.plist
    launchctl start com.github.runner

    print_status $GREEN "macOS service setup complete!"
}

# Function to setup Linux service
setup_linux() {
    print_status $GREEN "Setting up Linux systemd service..."

    # Get current user
    CURRENT_USER=$(logname || whoami)

    # Create service file
    cat > /etc/systemd/system/github-runner.service << EOL
[Unit]
Description=GitHub Actions Runner
After=docker.service
Requires=docker.service

[Service]
Type=simple
User=${CURRENT_USER}
Group=docker
WorkingDirectory=${SCRIPT_DIR}
Environment="PATH=/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin"
ExecStart=/usr/local/bin/docker-compose -f ${COMPOSE_FILE} up --build
ExecStop=/usr/local/bin/docker-compose -f ${COMPOSE_FILE} down
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOL

    # Set permissions
    chmod 644 /etc/systemd/system/github-runner.service

    # Create log directory
    mkdir -p /var/log/github-runner
    chown ${CURRENT_USER}:${CURRENT_USER} /var/log/github-runner

    # Reload systemd and enable service
    systemctl daemon-reload
    systemctl enable github-runner
    systemctl start github-runner

    print_status $GREEN "Linux service setup complete!"
}

# Add to main():
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_status $RED "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Main script
main() {
    print_status $YELLOW "GitHub Runner Service Setup"
    print_status $YELLOW "===========================\n"

    # Check if running as root
    check_root

    # Check Docker
    check_docker

    # Detect OS
    OS=$(detect_os)

    case $OS in
        "macos")
            setup_macos
            ;;
        "linux")
            setup_linux
            ;;
        *)
            print_status $RED "Unsupported operating system"
            exit 1
            ;;
    esac

    print_status $GREEN "\nSetup completed successfully!"
    print_status $YELLOW "\nTo monitor the service:"
    if [ "$OS" == "macos" ]; then
        echo "# Check if service is running:"
        echo "sudo launchctl list | grep com.github.runner"
        echo "  (If nothing shows up, the service is not running)"
        echo ""
        echo "# View service logs:"
        echo "tail -f ~/Library/Logs/github-runner.log"
        echo ""
        echo "# Check service status:"
        echo "sudo launchctl list | grep github"
        echo ""
        echo "# View Docker container logs:"
        echo "docker logs -f \$(docker ps | grep github-runner | awk '{print \$1}')"
    else
        echo "journalctl -u github-runner -f"
        echo "systemctl status github-runner"
    fi
}

# Run main function
main
