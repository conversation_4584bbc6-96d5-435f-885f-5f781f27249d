# GitHub Runner Service Setup Guide

## Prerequisites
1. Ensure Docker and <PERSON><PERSON> Compose are installed
2. Make sure all environment variables are properly set in your ~/.zshrc/.bashrc or .env file
3. Verify you have the correct permissions to access Docker

## Automatic Setup (Recommended)

The setup script will automatically:
1. Detect your operating system (macOS or Linux)
2. Create appropriate service files with correct paths
3. Set all necessary permissions
4. Start the service
5. Enable automatic startup

Run the setup:
```bash
sudo ./setup.sh
```

_ _ _

## macOS Manual Setup (LaunchDaemon)

### Initial Setup

1. Configure the plist file:
   - Open `com.github.runner.plist`
   - Update the paths to match your system:
     ```xml
     "/Users/<USER>/path/to/build-deploy_docker-compose.yml"
     ```
   - Update the `UserName` and `GroupName` to match your system user

2. Install the LaunchDaemon:
```bash
# Copy the plist file to LaunchDaemons
sudo cp com.github.runner.plist /Library/LaunchDaemons/

# Set correct ownership
sudo chown root:wheel /Library/LaunchDaemons/com.github.runner.plist

# Set correct permissions
sudo chmod 644 /Library/LaunchDaemons/com.github.runner.plist

# Create log files with correct permissions
touch ~/Library/Logs/github-runner.log
touch ~/Library/Logs/github-runner.err
chmod 644 ~/Library/Logs/github-runner.*
```

### Managing the macOS Service

```bash
# Load and start
sudo launchctl load /Library/LaunchDaemons/com.github.runner.plist
sudo launchctl start com.github.runner

# Monitor
# Check if service is running
sudo launchctl list | grep com.github.runner
# Note: If no output appears, the service is not running

# Check service file existence
ls -l /Library/LaunchDaemons/com.github.runner.plist

# View Docker container logs
docker logs -f $(docker ps | grep github-runner | awk '{print $1}')

# View service logs
tail -f ~/Library/Logs/github-runner.log
tail -f ~/Library/Logs/github-runner.log
tail -f ~/Library/Logs/github-runner.err

# Stop/Restart
sudo launchctl stop com.github.runner
sudo launchctl unload /Library/LaunchDaemons/com.github.runner.plist
sudo launchctl load /Library/LaunchDaemons/com.github.runner.plist
```

## Linux Manual Setup (systemd)

### Initial Setup

1. Create a systemd service file:
```bash
sudo nano /etc/systemd/system/github-runner.service
```

2. Add the following content:
```ini
[Unit]
Description=GitHub Actions Runner
After=docker.service
Requires=docker.service

[Service]
Type=simple
User=YOUR_USERNAME
Group=docker
WorkingDirectory=/path/to/your/runner/directory
Environment="PATH=/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin"
ExecStart=/usr/local/bin/docker-compose -f build-deploy_docker-compose.yml up --build
ExecStop=/usr/local/bin/docker-compose -f build-deploy_docker-compose.yml down
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

3. Setup permissions:
```bash
# Set correct permissions
sudo chmod 644 /etc/systemd/system/github-runner.service

# Create log directory if using system logging
sudo mkdir -p /var/log/github-runner
sudo chown YOUR_USERNAME:YOUR_GROUP /var/log/github-runner
```

### Managing the Linux Service

```bash
# Reload systemd to recognize new service
sudo systemctl daemon-reload

# Enable service to start on boot
sudo systemctl enable github-runner

# Start the service
sudo systemctl start github-runner

# Check status
sudo systemctl status github-runner

# View logs
journalctl -u github-runner -f

# Stop/Restart
sudo systemctl stop github-runner
sudo systemctl restart github-runner
```

## Troubleshooting

### macOS
If the service fails to start:
1. Check the log files in `~/Library/Logs/github-runner.*`
2. Verify all paths in the plist file are correct
3. Ensure Docker is running
4. Verify environment variables are properly set
5. Check permissions on all referenced directories and files

### Linux
If the service fails to start:
1. Check the logs: `journalctl -u github-runner -f`
2. Verify Docker service is running: `systemctl status docker`
3. Check permissions: Ensure your user is in the docker group
4. Verify paths in the service file are correct
5. Check system logs: `dmesg | tail`

## Common Issues

1. Docker permissions:
```bash
# Add user to docker group (Linux)
sudo usermod -aG docker $USER
newgrp docker
```

2. Environment variables not loading:
- For macOS: Add them to ~/.zshrc
- For Linux: Add them to /etc/environment or the systemd service file

3. Service fails to start automatically:
- macOS: Check Console.app for system logs
- Linux: Check `journalctl -xb` for boot logs

## Notes
- The service is configured to start automatically on system boot
- It will attempt to restart on failure
- Log locations:
  - macOS: `~/Library/Logs/github-runner.*`
  - Linux: `journalctl` or system logs
