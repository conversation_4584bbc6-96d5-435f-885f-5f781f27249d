services:
  runner:
    build:
      context: .
      dockerfile: Dockerfile
    platform: linux/amd64 # Specify platform explicitly
    restart: unless-stopped
    deploy:
      replicas: 4
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    networks:
      - runner-network
    environment:
      # Required configuration
      REPO_URL: https://github.com/Divinci-AI/server
      RUNNER_TOKEN: ${ACCESS_TOKEN}
      RUNNER_NAME: ${RUNNER_NAME:-divinci-docker-runner}
      GITHUB_PAT: ${GITHUB_PAT}
      DIVINCI_PAT_3: ${DIVINCI_PAT_3}

      # Runner scope
      RUNNER_SCOPE: "repo"

      # Root configuration
      RUN_AS_ROOT: "true"
      RUNNER_ALLOW_RUNASROOT: "1"

      # Non-ephemeral configuration
      EPHEMERAL: "false"

      # Debug settings (temporary)
      DEBUG_OUTPUT: "true"

      # Work directory and persistence
      RUNNER_WORKDIR: /tmp/runner/work
      CONFIGURED_ACTIONS_RUNNER_FILES_DIR: /actions-runner/data # Updated path

      # Optional configurations
      LABELS: "self-hosted,linux,docker,X64"
      RUNNER_GROUP: ${RUNNER_GROUP:-Default}
      DISABLE_AUTO_UPDATE: "true"
      DISABLE_RUNNER_UPDATE: "true"

    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./runner-data:/actions-runner/data:rw
      - ./runner-work:/tmp/runner/work:rw
      - ~/.cache/ms-playwright:/root/.cache/ms-playwright:rw
      - ./entrypoint.sh:/entrypoint.sh:ro
      - ./healthy.sh:/actions-runner/healthy.sh:ro

    security_opt:
      - label:disable

    init: true

networks:
  runner-network:
    driver: bridge
