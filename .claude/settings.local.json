{"permissions": {"allow": ["<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(pnpm test:*)", "Bash(NODE_ENV=test pnpm test -- tests/white-label/rag/file/methods/chunk-and-save/chunk-and-save-workflow.test.ts)", "Bash(NODE_ENV=test pnpm test -- tests/white-label/rag/file/methods/chunk-and-save/utilities.test.ts tests/white-label/rag/file/methods/chunk-and-save/chunk-and-save-workflow.edge-cases.test.ts)", "Bash(NODE_ENV=test pnpm test -- tests/white-label/rag/file/methods/chunk-and-save/utilities.test.ts)", "Bash(ls:*)", "Bash(pnpm remove:*)", "Bash(pnpm add:*)", "Bash(pnpm install:*)", "Bash(pnpm prepare)", "Bash(pnpm why:*)", "<PERSON><PERSON>(chmod:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(mv:*)", "Bash(npm install:*)", "Bash(npm run test:unit:*)", "Bash(npm test)", "<PERSON><PERSON>(npx jest:*)", "Bash(pnpm install)", "Bash(pnpm test:vitest:*)", "Bash(rm:*)", "Bash(git add:*)", "Bash(git push:*)", "Bash(git commit:*)", "Bash(git grep:*)", "<PERSON><PERSON>(launchctl:*)", "Bash(git pull:*)", "Bash(git rev-parse:*)", "Bash(gh auth:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(gh run list:*)", "Bash(cp:*)", "<PERSON><PERSON>(docker run:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker compose:*)", "<PERSON><PERSON>(docker pull:*)", "WebFetch(domain:docs.github.com)", "Bash(./generate-repo-token.sh:*)", "<PERSON><PERSON>(./start-arm64-runners.sh:*)", "<PERSON><PERSON>(docker inspect:*)", "<PERSON><PERSON>(./start-official-runners.sh:*)", "<PERSON><PERSON>(./stop-arm64-runners.sh:*)", "Bash(npm view:*)", "<PERSON><PERSON>(npx playwright test:*)", "<PERSON>sh(git check-ignore:*)", "Bash(docker volume:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(npm ls:*)", "Bash(npm init:*)", "Bash(vitest --version)", "<PERSON><PERSON>(curl:*)", "Bash(docker network:*)", "<PERSON><PERSON>(docker cp:*)", "<PERSON><PERSON>(python3:*)", "Bash(pip3 install:*)", "Bash(/Users/<USER>/Documents/server/test-chunks-workflow.sh:*)", "Bash(pnpm run:*)", "Bash(npx tsc:*)", "Bash(pnpm list:*)", "Bash(/Users/<USER>/Documents/server/test-curl.sh)", "Bash(node:*)", "Bash(./test-scripts/test-minio-connectivity.sh:*)", "Bash(git checkout:*)", "Bash(npm run typecheck:*)", "Bash(pnpm typecheck:*)", "Bash(pnpm build:*)", "Bash(gh pr view:*)"], "deny": []}}