import { Request, Response, NextFunction } from "express";
import { applyEnhancedCorsHeaders } from "@divinci-ai/server-globals";

/**
 * Middleware to add CORS headers for local development
 * Uses the centralized CORS functions from server-globals for consistency
 */
export function localCorsMiddleware(req: Request, res: Response, next: NextFunction){
  // Only apply in local environment
  if(process.env.ENVIRONMENT === "local" || process.env.NODE_ENV === "development" || process.env.NODE_ENV === "local") {
    // Log remote address for debugging
    console.log(`🪪 remote address: `, req.socket.remoteAddress);

    // Use the centralized CORS function from server-globals
    // This ensures consistency across all services
    const isAllowed = applyEnhancedCorsHeaders(req, res);

    // If this was an OPTIONS request, it was already handled by applyEnhancedCorsHeaders
    if (req.method === "OPTIONS") {
      return; // Response already sent
    }

    // In BARE METAL MODE, we still need to authenticate Cloudflare Workers.
    // In production, this is handled by Cloudflare's authentication system.
    // In local development, we use a special header to simulate this authentication.
    if (req.headers["cloudflare-worker-x-dev-auth"]) {
      console.log(`🔑 Cloudflare Worker dev auth detected - setting auth payload`);
      // Add a proper auth payload for local development
      (req as any).auth = {
        payload: {
          sub: "cloudflare-worker-local-dev",
          iat: Math.floor(Date.now() / 1000),
          exp: Math.floor(Date.now() / 1000) + 3600,
          iss: "https://divinci.us.auth0.com/",
          aud: "https://api.divinci.ai"
        }
      };
    } else if (!req.headers.authorization) {
      // Check and log if no auth is present
      console.log(`🙅🏻‍♂️🪪 No auth in req: `, req.headers.authorization);
    }
  }

  // Continue to the next middleware
  next();
}
